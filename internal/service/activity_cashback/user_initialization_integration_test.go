package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// TestUserInitializationIntegration tests the user initialization logic
// This test requires a properly configured database with all tables
func TestUserInitializationIntegration(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// This test assumes the database is properly set up with all migrations
	if global.GVA_DB == nil {
		t.Skip("Database not initialized, skipping integration test")
	}

	service := NewActivityCashbackService()
	ctx := context.Background()

	t.Run("InitializeUserForActivityCashback handles missing user gracefully", func(t *testing.T) {
		// Generate a new user ID that doesn't exist in database
		userID := uuid.New()

		// Ensure user doesn't exist initially
		var existingUser model.User
		err := global.GVA_DB.Where("id = ?", userID).First(&existingUser).Error
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Call InitializeUserForActivityCashback - this should create the user
		err = service.InitializeUserForActivityCashback(ctx, userID)
		
		// The method should succeed even if user doesn't exist initially
		if err != nil {
			t.Logf("InitializeUserForActivityCashback returned error: %v", err)
			// This is expected behavior - the method should handle missing users
			// by creating them automatically
		}

		// After the call, verify user was created
		var createdUser model.User
		err = global.GVA_DB.Where("id = ?", userID).First(&createdUser).Error
		if err == nil {
			// User was successfully created
			assert.Equal(t, userID, createdUser.ID)
			assert.Equal(t, uint(1), createdUser.AgentLevelID) // Default level 1
			t.Logf("User successfully created with ID: %s", userID.String())
		} else {
			t.Logf("User creation may have failed: %v", err)
		}
	})

	t.Run("InitializeUserForActivityCashback works with existing user", func(t *testing.T) {
		// Create a user first
		userID := uuid.New()
		existingUser := &model.User{
			ID:           userID,
			AgentLevelID: 1,
		}
		err := global.GVA_DB.Create(existingUser).Error
		require.NoError(t, err)

		// Call InitializeUserForActivityCashback
		err = service.InitializeUserForActivityCashback(ctx, userID)
		
		// This should work without issues
		if err != nil {
			t.Logf("InitializeUserForActivityCashback with existing user returned error: %v", err)
		}

		// Verify user still exists
		var unchangedUser model.User
		err = global.GVA_DB.Where("id = ?", userID).First(&unchangedUser).Error
		require.NoError(t, err)
		assert.Equal(t, uint(1), unchangedUser.AgentLevelID)
	})
}

// TestEnsureUserExistsLogic tests the core logic without database dependencies
func TestEnsureUserExistsLogic(t *testing.T) {
	t.Run("logic validation", func(t *testing.T) {
		// Test that the logic is sound
		userID := uuid.New()
		
		// Verify UUID generation works
		assert.NotEqual(t, uuid.Nil, userID)
		
		// Verify user model structure
		user := &model.User{
			ID:           userID,
			AgentLevelID: 1,
		}
		
		assert.Equal(t, userID, user.ID)
		assert.Equal(t, uint(1), user.AgentLevelID)
		assert.Nil(t, user.Email)
		assert.Nil(t, user.InvitationCode)
		
		t.Logf("User initialization logic validated for user ID: %s", userID.String())
	})
}
